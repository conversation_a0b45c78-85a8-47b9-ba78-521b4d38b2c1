# Q精华 Hub

一个现代化的QQ精华消息展示平台，采用瀑布流布局展示精华内容，支持搜索、筛选和排序功能。

## 功能特性

### 🎨 界面设计
- **瀑布流布局**：响应式瀑布流设计，自适应不同屏幕尺寸
- **明暗主题**：支持明暗主题切换，自动适配系统偏好
- **现代化UI**：使用Tailwind CSS构建的现代化界面
- **流畅动画**：卡片悬停效果和加载动画

### 🔍 功能组件
- **搜索功能**：支持按内容、发送者、设置者、群组名称搜索
- **排序选项**：按最新、最早、最热排序
- **内容筛选**：按全部、文字、图片类型筛选
- **无限滚动**：自动加载更多内容

### 📱 响应式设计
- **移动端优化**：完美适配手机屏幕
- **平板适配**：针对平板设备优化布局
- **桌面端**：充分利用大屏幕空间

## 技术栈

- **框架**：Next.js 15.5.2 (App Router)
- **UI库**：React 19.1.0
- **样式**：Tailwind CSS 4.1.12
- **语言**：TypeScript 5
- **字体**：Geist字体系列

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页面
├── components/            # React组件
│   ├── Header.tsx         # 顶部功能区
│   ├── MasonryGrid.tsx    # 瀑布流布局
│   ├── PinCard.tsx        # 精华消息卡片
│   ├── SmartAvatar.tsx    # 智能头像组件
│   ├── GifImage.tsx       # GIF动图组件
│   └── LoadingSpinner.tsx # 加载动画
├── services/              # 数据服务
│   ├── dataService.ts     # 数据加载服务
│   └── avatarService.ts   # 头像配置服务
├── types/                 # 类型定义
│   ├── jsonData.ts        # JSON数据类型
│   └── avatarConfig.ts    # 头像配置类型
└── utils/                 # 工具函数
    ├── avatarUtils.ts     # 头像生成工具
    └── dataTransform.ts   # 数据转换工具
```

## 快速开始

### 安装依赖

```bash
npm install
# 或
yarn install
# 或
pnpm install
```

### 启动开发服务器

```bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
npm start
```

## 组件说明

### PinCard 组件
精华消息卡片组件，支持：
- 文本和图片内容展示
- 发送者和设置者信息
- 时间显示（相对时间）
- 群组信息
- 悬停动画效果

### Header 组件
顶部功能区组件，包含：
- 搜索框（支持实时搜索）
- 排序选择器
- 内容类型筛选器
- 明暗主题切换按钮

### MasonryGrid 组件
瀑布流布局组件，特性：
- 响应式列数（1-4列）
- 无限滚动加载
- 自动高度适配
- 加载状态显示

## 自定义配置

### 主题配置
在 `src/app/globals.css` 中可以自定义：
- 颜色变量
- 动画效果
- 响应式断点

### 数据源
数据来源于 `public/data/pins.json` 文件：
- 真实的QQ精华消息数据
- 支持文本、图片、GIF等多种内容类型
- 使用Python脚本管理头像配置

## 部署

推荐使用 [Vercel](https://vercel.com) 部署：

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 自动部署完成

也可以部署到其他支持 Next.js 的平台。

## 开发说明

### 添加新功能
1. 在 `src/components/` 中创建新组件
2. 在 `src/app/page.tsx` 中集成组件
3. 更新类型定义（如需要）

### 样式修改
- 使用 Tailwind CSS 类名
- 自定义样式写在 `globals.css`
- 确保明暗主题兼容性

## 许可证

MIT License
